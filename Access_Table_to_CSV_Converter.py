import os
import time
import pyodbc
import pandas as pd
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# === CONFIGURATION ===
WATCH_FOLDER = r"B:\MINER 01 TEST"   # Folder with new Access files
CSV_OUTPUT_FOLDER = r"B:\MINER 01 CSV DATABASE"   # Folder for CSV export
TABLES_TO_EXPORT = ["ParamFactValue", "TestNo","OriginalData"]        # The tables you want

# Ensure output folder exists
os.makedirs(CSV_OUTPUT_FOLDER, exist_ok=True)

# === FILE HANDLER ===
class AccessHandler(FileSystemEventHandler):
    def on_created(self, event):
        if not event.is_directory and event.src_path.lower().endswith((".mdb", ".accdb")):
            print(f"New file detected: {event.src_path}")
            process_access_file(event.src_path)

def process_access_file(filepath):
    try:
        # Connect to Access DB
        conn_str = (
            r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
            f"DBQ={filepath};"
        )
        conn = pyodbc.connect(conn_str)
        
        for table in TABLES_TO_EXPORT:
            try:
                df = pd.read_sql(f"SELECT * FROM [{table}]", conn)

                # Create a subfolder for this table
                table_folder = os.path.join(CSV_OUTPUT_FOLDER, table)
                os.makedirs(table_folder, exist_ok=True)

                # Save CSV into the subfolder
                output_path = os.path.join(
                    table_folder,
                    f"{os.path.splitext(os.path.basename(filepath))[0]}_{table}.csv"
                )
                df.to_csv(output_path, index=False)
                print(f"Exported {table} → {output_path}")

            except Exception as e:
                print(f"⚠️ Could not export table {table}: {e}")
        
        conn.close()
    except Exception as e:
        print(f"❌ Failed to process {filepath}: {e}")

# === START MONITORING ===
if __name__ == "__main__":
    print(f"Watching folder: {WATCH_FOLDER}")
    event_handler = AccessHandler()
    observer = Observer()
    observer.schedule(event_handler, WATCH_FOLDER, recursive=False)
    observer.start()
    
    try:
        while True:
            time.sleep(2)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()
