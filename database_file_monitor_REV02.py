#!/usr/bin/env python3
"""
Access Database File Monitor and Copy Service

Monitors a local folder for new Microsoft Access database files (.mdb, .accdb)
and automatically copies them to a server location with proper error handling,
logging, and conflict resolution.

Author: Generated for QR Generator Project
Version: 1.0
"""

import os
import sys
import time
import shutil
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Set, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileCreatedEvent, FileMovedEvent


class DatabaseFileHandler(FileSystemEventHandler):
    """File system event handler for Access database files"""
    
    def __init__(self, source_path: str, destination_path: str, logger: logging.Logger):
        self.source_path = Path(source_path)
        self.destination_path = Path(destination_path)
        self.logger = logger
        self.valid_extensions = {'.mdb', '.accdb'}
        self.processed_files: Set[str] = set()
        
        # Ensure destination directory exists
        self.destination_path.mkdir(parents=True, exist_ok=True)
        
    def is_database_file(self, file_path: str) -> bool:
        """Check if file is an Access database file"""
        return Path(file_path).suffix.lower() in self.valid_extensions
    
    def copy_database_file(self, source_file: Path) -> bool:
        """
        Copy database file to destination with conflict resolution
        
        Args:
            source_file: Path to source database file
            
        Returns:
            bool: True if copy successful, False otherwise
        """
        try:
            # Skip if already processed
            file_key = f"{source_file.name}_{source_file.stat().st_mtime}"
            if file_key in self.processed_files:
                return True
                
            destination_file = self.destination_path / source_file.name
            
            # Handle file conflicts
            if destination_file.exists():
                # Compare file sizes and modification times
                src_stat = source_file.stat()
                dst_stat = destination_file.stat()
                
                if (src_stat.st_size == dst_stat.st_size and 
                    abs(src_stat.st_mtime - dst_stat.st_mtime) < 2):
                    self.logger.info(f"File {source_file.name} already exists and appears identical - skipping")
                    self.processed_files.add(file_key)
                    return True
                
                # Create backup of existing file
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"{destination_file.stem}_backup_{timestamp}{destination_file.suffix}"
                backup_path = self.destination_path / backup_name
                
                shutil.copy2(destination_file, backup_path)
                self.logger.info(f"Created backup: {backup_name}")
            
            # Copy the file with metadata preservation
            shutil.copy2(source_file, destination_file)
            
            # Verify copy was successful
            if destination_file.exists():
                src_size = source_file.stat().st_size
                dst_size = destination_file.stat().st_size
                
                if src_size == dst_size:
                    self.logger.info(f"✅ Successfully copied: {source_file.name} ({src_size:,} bytes)")
                    self.processed_files.add(file_key)
                    return True
                else:
                    self.logger.error(f"❌ Copy verification failed: size mismatch for {source_file.name}")
                    return False
            else:
                self.logger.error(f"❌ Copy failed: destination file not found for {source_file.name}")
                return False
                
        except PermissionError as e:
            self.logger.error(f"❌ Permission denied copying {source_file.name}: {e}")
            return False
        except OSError as e:
            self.logger.error(f"❌ OS error copying {source_file.name}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error copying {source_file.name}: {e}")
            return False
    
    def on_created(self, event):
        """Handle file creation events"""
        if not event.is_directory and self.is_database_file(event.src_path):
            self.logger.info(f"📁 New database file detected: {Path(event.src_path).name}")
            
            # Wait a moment for file to be fully written
            time.sleep(5)
            
            source_file = Path(event.src_path)
            if source_file.exists():
                self.copy_database_file(source_file)
    
    def on_moved(self, event):
        """Handle file move events (includes renames)"""
        if not event.is_directory and self.is_database_file(event.dest_path):
            self.logger.info(f"📁 Database file moved/renamed: {Path(event.dest_path).name}")
            
            # Wait a moment for file to be fully written
            time.sleep(5)
            
            source_file = Path(event.dest_path)
            if source_file.exists():
                self.copy_database_file(source_file)


class DatabaseFileMonitor:
    """Main monitor class for database file copying service"""
    
    def __init__(self, source_path: str, destination_path: str, log_level: str = "INFO"):
        self.source_path = source_path
        self.destination_path = destination_path
        self.observer = None
        
        # Setup logging
        self.setup_logging(log_level)
        
        # Validate paths
        self.validate_paths()
        
        # Create event handler
        self.event_handler = DatabaseFileHandler(
            source_path, destination_path, self.logger
        )
    
    def setup_logging(self, log_level: str):
        """Setup logging configuration"""
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        log_file = log_dir / f"database_monitor_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 Database File Monitor initialized")
    
    def validate_paths(self):
        """Validate source and destination paths"""
        source = Path(self.source_path)
        destination = Path(self.destination_path)
        
        if not source.exists():
            raise FileNotFoundError(f"Source path does not exist: {self.source_path}")
        
        if not source.is_dir():
            raise NotADirectoryError(f"Source path is not a directory: {self.source_path}")
        
        # Try to create destination if it doesn't exist
        try:
            destination.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise PermissionError(f"Cannot create destination directory: {e}")
        
        # Test write access to destination
        test_file = destination / "test_write_access.tmp"
        try:
            test_file.touch()
            test_file.unlink()
        except Exception as e:
            raise PermissionError(f"No write access to destination: {e}")
        
        self.logger.info(f"✅ Source path validated: {self.source_path}")
        self.logger.info(f"✅ Destination path validated: {self.destination_path}")
    
    def scan_existing_files(self):
        """Scan for existing database files and copy them"""
        self.logger.info("🔍 Scanning for existing database files...")
        
        source = Path(self.source_path)
        count = 0
        
        for file_path in source.iterdir():
            if (file_path.is_file() and 
                file_path.suffix.lower() in {'.mdb', '.accdb'}):
                
                self.logger.info(f"📄 Found existing file: {file_path.name}")
                if self.event_handler.copy_database_file(file_path):
                    count += 1
        
        self.logger.info(f"✅ Initial scan complete. Processed {count} files.")
    
    def start_monitoring(self):
        """Start continuous file system monitoring"""
        self.logger.info("👀 Starting continuous monitoring...")
        
        self.observer = Observer()
        self.observer.schedule(
            self.event_handler,
            self.source_path,
            recursive=False
        )
        
        self.observer.start()
        self.logger.info(f"🎯 Monitoring active for: {self.source_path}")
        
        try:
            while True:
                time.sleep(10)  # Check every 10 seconds
                
        except KeyboardInterrupt:
            self.logger.info("⏹️ Monitoring stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Monitoring error: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop file system monitoring"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.logger.info("🛑 File monitoring stopped")
    
    def run_once(self):
        """Run once to copy existing files without continuous monitoring"""
        self.logger.info("🔄 Running in single-scan mode")
        self.scan_existing_files()
        self.logger.info("✅ Single scan completed")
    
    def run_continuous(self):
        """Run continuous monitoring with initial scan"""
        self.logger.info("🔄 Running in continuous monitoring mode")
        
        # First scan existing files
        self.scan_existing_files()
        
        # Then start monitoring for new files
        self.start_monitoring()


def main():
    """Main entry point with command line argument parsing"""
    parser = argparse.ArgumentParser(
        description="Monitor and copy Access database files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run with default paths (continuous monitoring)
  python database_file_monitor.py
  
  # Run once with custom paths
  python database_file_monitor.py --source "C:\\Custom\\Path" --destination "D:\\Backup" --once
  
  # Run with debug logging
  python database_file_monitor.py --log-level DEBUG
        """
    )
    
    parser.add_argument(
        '--source', '-s',
        default=r"C:\Users\<USER>\OneDrive - Milson Elastomeric Solutions (Pty) Ltd\Documents\QC Database\BP TEST DATABASE",
        help='Source folder to monitor (default: %(default)s)'
    )
    
    parser.add_argument(
        '--destination', '-d',
        default=r"B:\BP TEST DATABASE",
        help='Destination folder for copies (default: %(default)s)'
    )
    
    parser.add_argument(
        '--once',
        action='store_true',
        help='Run once to copy existing files, then exit'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: %(default)s)'
    )
    
    args = parser.parse_args()
    
    try:
        # Create monitor instance
        monitor = DatabaseFileMonitor(
            source_path=args.source,
            destination_path=args.destination,
            log_level=args.log_level
        )
        
        # Run based on mode
        if args.once:
            monitor.run_once()
        else:
            monitor.run_continuous()
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()