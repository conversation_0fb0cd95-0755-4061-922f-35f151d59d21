#!/usr/bin/env python3
"""
Robust Access Database File Monitor and Copy Service

Enhanced version with robust file operations that prevent data corruption through:
- Proper file operation verification instead of fixed sleep delays
- Retry mechanisms with exponential backoff for failed operations
- File integrity checks (size stability, write completion verification)
- Comprehensive error handling and logging for file operations
- Atomic operations where possible to prevent corruption
- File locking mechanisms to prevent concurrent access

This version replaces the problematic sleep-based approach with intelligent
file stability checking and robust operation handling.

Author: Generated for QR Generator Project
Version: 2.0 (Robust Operations)
"""

import os
import sys
import time
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Import robust file operations
from robust_file_operations import (
    RobustFileOperations, 
    create_default_config,
    create_performance_config,
    create_high_integrity_config,
    FileOperationError,
    FileIntegrityError,
    RetryExhaustedError
)
import monitor_config


class RobustDatabaseFileHandler(FileSystemEventHandler):
    """Enhanced file system event handler with robust operations for Access database files"""
    
    def __init__(self, source_path: str, destination_path: str, logger: logging.Logger):
        self.source_path = Path(source_path)
        self.destination_path = Path(destination_path)
        self.logger = logger
        self.valid_extensions = {'.mdb', '.accdb'}
        self.processed_files: Set[str] = set()
        
        # Ensure destination directory exists
        self.destination_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize robust file operations
        self.robust_ops = self._create_robust_operations()
        
        # Log configuration being used
        self._log_configuration()
    
    def _create_robust_operations(self) -> RobustFileOperations:
        """Create robust file operations instance based on configuration"""
        # Get profile from config
        profile = getattr(monitor_config, 'ROBUST_PROFILE', 'default')
        
        if profile == 'performance':
            config = create_performance_config()
            self.logger.info("🚀 Using PERFORMANCE profile for robust operations")
        elif profile == 'high_integrity':
            config = create_high_integrity_config()
            self.logger.info("🔒 Using HIGH_INTEGRITY profile for robust operations")
        else:
            config = create_default_config()
            self.logger.info("⚖️ Using DEFAULT profile for robust operations")
        
        # Override with specific config values if they exist
        if hasattr(monitor_config, 'ROBUST_RETRY_MAX_ATTEMPTS'):
            config.retry_config.max_attempts = monitor_config.ROBUST_RETRY_MAX_ATTEMPTS
        if hasattr(monitor_config, 'ROBUST_RETRY_INITIAL_DELAY'):
            config.retry_config.initial_delay = monitor_config.ROBUST_RETRY_INITIAL_DELAY
        if hasattr(monitor_config, 'ROBUST_RETRY_MAX_DELAY'):
            config.retry_config.max_delay = monitor_config.ROBUST_RETRY_MAX_DELAY
        if hasattr(monitor_config, 'ROBUST_RETRY_BACKOFF_FACTOR'):
            config.retry_config.backoff_factor = monitor_config.ROBUST_RETRY_BACKOFF_FACTOR
        if hasattr(monitor_config, 'ROBUST_RETRY_USE_JITTER'):
            config.retry_config.jitter = monitor_config.ROBUST_RETRY_USE_JITTER
        
        if hasattr(monitor_config, 'ROBUST_CHECK_SIZE_STABILITY'):
            config.integrity_config.check_size_stability = monitor_config.ROBUST_CHECK_SIZE_STABILITY
        if hasattr(monitor_config, 'ROBUST_SIZE_STABILITY_CHECKS'):
            config.integrity_config.size_stability_checks = monitor_config.ROBUST_SIZE_STABILITY_CHECKS
        if hasattr(monitor_config, 'ROBUST_SIZE_STABILITY_INTERVAL'):
            config.integrity_config.size_stability_interval = monitor_config.ROBUST_SIZE_STABILITY_INTERVAL
        if hasattr(monitor_config, 'ROBUST_VERIFY_CHECKSUM'):
            config.integrity_config.verify_checksum = monitor_config.ROBUST_VERIFY_CHECKSUM
        if hasattr(monitor_config, 'ROBUST_CHECKSUM_ALGORITHM'):
            config.integrity_config.checksum_algorithm = monitor_config.ROBUST_CHECKSUM_ALGORITHM
        if hasattr(monitor_config, 'ROBUST_MAX_WAIT_TIME'):
            config.integrity_config.max_wait_time = monitor_config.ROBUST_MAX_WAIT_TIME
        
        if hasattr(monitor_config, 'ROBUST_USE_FILE_LOCKING'):
            config.use_file_locking = monitor_config.ROBUST_USE_FILE_LOCKING
        if hasattr(monitor_config, 'ROBUST_ATOMIC_OPERATIONS'):
            config.atomic_operations = monitor_config.ROBUST_ATOMIC_OPERATIONS
        if hasattr(monitor_config, 'ROBUST_PRESERVE_METADATA'):
            config.preserve_metadata = monitor_config.ROBUST_PRESERVE_METADATA
        if hasattr(monitor_config, 'ROBUST_VERIFY_AFTER_OPERATION'):
            config.verify_after_operation = monitor_config.ROBUST_VERIFY_AFTER_OPERATION
        
        return RobustFileOperations(config, self.logger)
    
    def _log_configuration(self):
        """Log the current robust operations configuration"""
        config = self.robust_ops.config
        self.logger.info("🔧 Robust Operations Configuration:")
        self.logger.info(f"   Retry: max_attempts={config.retry_config.max_attempts}, "
                        f"initial_delay={config.retry_config.initial_delay}s, "
                        f"max_delay={config.retry_config.max_delay}s")
        self.logger.info(f"   Integrity: size_stability={config.integrity_config.check_size_stability}, "
                        f"checksum={config.integrity_config.verify_checksum}, "
                        f"max_wait={config.integrity_config.max_wait_time}s")
        self.logger.info(f"   Operations: file_locking={config.use_file_locking}, "
                        f"atomic={config.atomic_operations}, "
                        f"verify_after={config.verify_after_operation}")
        
    def is_database_file(self, file_path: str) -> bool:
        """Check if file is an Access database file"""
        return Path(file_path).suffix.lower() in self.valid_extensions

    def copy_database_file(self, source_file: Path) -> bool:
        """
        Copy database file to destination using robust operations with conflict resolution

        Args:
            source_file: Path to source database file

        Returns:
            bool: True if copy successful, False otherwise
        """
        try:
            # Skip if already processed
            file_key = f"{source_file.name}_{source_file.stat().st_mtime}"
            if file_key in self.processed_files:
                self.logger.debug(f"File already processed, skipping: {source_file.name}")
                return True

            destination_file = self.destination_path / source_file.name

            # Handle file conflicts
            if destination_file.exists():
                # Compare file sizes and modification times
                src_stat = source_file.stat()
                dst_stat = destination_file.stat()

                if (src_stat.st_size == dst_stat.st_size and
                    abs(src_stat.st_mtime - dst_stat.st_mtime) < 2):
                    self.logger.info(f"File {source_file.name} already exists and appears identical - skipping")
                    self.processed_files.add(file_key)
                    return True

                # Create backup of existing file using robust operations
                if getattr(monitor_config, 'CREATE_BACKUPS', True):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_suffix = getattr(monitor_config, 'BACKUP_SUFFIX', '_backup')
                    backup_name = f"{destination_file.stem}{backup_suffix}_{timestamp}{destination_file.suffix}"
                    backup_path = self.destination_path / backup_name

                    try:
                        # Use robust operations for backup creation
                        self.logger.info(f"Creating backup before overwrite: {backup_name}")
                        backup_success = self.robust_ops.robust_copy(destination_file, backup_path)
                        if backup_success:
                            self.logger.info(f"✅ Created backup: {backup_name}")
                        else:
                            self.logger.warning(f"⚠️ Backup creation failed for {destination_file.name}")
                    except (FileOperationError, FileIntegrityError, RetryExhaustedError) as e:
                        self.logger.warning(f"⚠️ Backup creation failed: {e}")

            # Perform robust copy operation
            self.logger.info(f"🔄 Starting robust copy operation: {source_file.name}")
            start_time = time.time()

            success = self.robust_ops.robust_copy(source_file, destination_file)

            if success:
                # Get final file size for logging
                final_size = destination_file.stat().st_size
                elapsed_time = time.time() - start_time
                self.logger.info(f"✅ Successfully copied with robust operations: {source_file.name} "
                               f"({final_size:,} bytes) in {elapsed_time:.2f}s")
                self.processed_files.add(file_key)
                return True
            else:
                self.logger.error(f"❌ Robust copy operation returned False for {source_file.name}")
                return False

        except FileOperationError as e:
            self.logger.error(f"❌ File operation error copying {source_file.name}: {e}")
            return False
        except FileIntegrityError as e:
            self.logger.error(f"❌ File integrity error copying {source_file.name}: {e}")
            return False
        except RetryExhaustedError as e:
            self.logger.error(f"❌ Retry exhausted copying {source_file.name}: {e}")
            return False
        except PermissionError as e:
            self.logger.error(f"❌ Permission denied copying {source_file.name}: {e}")
            return False
        except OSError as e:
            self.logger.error(f"❌ OS error copying {source_file.name}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error copying {source_file.name}: {e}")
            return False

    def on_created(self, event):
        """Handle file creation events with robust file operations"""
        if not event.is_directory and self.is_database_file(event.src_path):
            self.logger.info(f"📁 New database file detected: {Path(event.src_path).name}")

            source_file = Path(event.src_path)
            if source_file.exists():
                # Robust operations will handle file stability checking internally
                # No more problematic sleep delays!
                self.copy_database_file(source_file)
            else:
                self.logger.warning(f"⚠️ File no longer exists after creation event: {source_file.name}")

    def on_moved(self, event):
        """Handle file move events (includes renames) with robust file operations"""
        if not event.is_directory and self.is_database_file(event.dest_path):
            self.logger.info(f"📁 Database file moved/renamed: {Path(event.dest_path).name}")

            source_file = Path(event.dest_path)
            if source_file.exists():
                # Robust operations will handle file stability checking internally
                # No more problematic sleep delays!
                self.copy_database_file(source_file)
            else:
                self.logger.warning(f"⚠️ File no longer exists after move event: {source_file.name}")


class RobustDatabaseFileMonitor:
    """Enhanced monitor class with robust file operations for database file copying service"""

    def __init__(self, source_path: str, destination_path: str, log_level: str = "INFO"):
        self.source_path = source_path
        self.destination_path = destination_path
        self.observer = None

        # Setup logging
        self.setup_logging(log_level)

        # Validate paths
        self.validate_paths()

        # Create enhanced event handler
        self.event_handler = RobustDatabaseFileHandler(
            source_path, destination_path, self.logger
        )

    def setup_logging(self, log_level: str):
        """Setup logging configuration"""
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Configure logging
        log_file = log_dir / f"database_monitor_robust_{datetime.now().strftime('%Y%m%d')}.log"

        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 Robust Database File Monitor initialized")
        self.logger.info("🔧 This version uses intelligent file stability checking instead of fixed delays")

    def validate_paths(self):
        """Validate source and destination paths"""
        source = Path(self.source_path)
        destination = Path(self.destination_path)

        if not source.exists():
            raise FileNotFoundError(f"Source path does not exist: {self.source_path}")

        if not source.is_dir():
            raise NotADirectoryError(f"Source path is not a directory: {self.source_path}")

        # Try to create destination if it doesn't exist
        try:
            destination.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise PermissionError(f"Cannot create destination directory: {e}")

        # Test write access to destination
        test_file = destination / "test_write_access.tmp"
        try:
            test_file.touch()
            test_file.unlink()
        except Exception as e:
            raise PermissionError(f"No write access to destination: {e}")

        self.logger.info(f"✅ Source path validated: {self.source_path}")
        self.logger.info(f"✅ Destination path validated: {self.destination_path}")

    def scan_existing_files(self):
        """Scan for existing database files and copy them using robust operations"""
        self.logger.info("🔍 Scanning for existing database files...")

        source = Path(self.source_path)
        count = 0

        for file_path in source.iterdir():
            if (file_path.is_file() and
                file_path.suffix.lower() in {'.mdb', '.accdb'}):

                self.logger.info(f"📄 Found existing file: {file_path.name}")
                if self.event_handler.copy_database_file(file_path):
                    count += 1

        self.logger.info(f"✅ Initial scan complete. Processed {count} files using robust operations.")

    def start_monitoring(self):
        """Start continuous file system monitoring with robust operations"""
        self.logger.info("👀 Starting continuous monitoring with robust file operations...")

        self.observer = Observer()
        self.observer.schedule(
            self.event_handler,
            self.source_path,
            recursive=False
        )

        self.observer.start()
        self.logger.info(f"🎯 Robust monitoring active for: {self.source_path}")

        try:
            while True:
                time.sleep(10)  # Check every 10 seconds

        except KeyboardInterrupt:
            self.logger.info("⏹️ Monitoring stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Monitoring error: {e}")
        finally:
            self.stop_monitoring()

    def stop_monitoring(self):
        """Stop file system monitoring"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.logger.info("🛑 Robust file monitoring stopped")

    def run_once(self):
        """Run once to copy existing files without continuous monitoring"""
        self.logger.info("🔄 Running in single-scan mode with robust operations")
        self.scan_existing_files()
        self.logger.info("✅ Single scan completed")

    def run_continuous(self):
        """Run continuous monitoring with initial scan using robust operations"""
        self.logger.info("🔄 Running in continuous monitoring mode with robust operations")

        # First scan existing files
        self.scan_existing_files()

        # Then start monitoring for new files
        self.start_monitoring()


def main():
    """Main entry point with command line argument parsing"""
    parser = argparse.ArgumentParser(
        description="Robust Monitor and Copy for Access database files - prevents corruption with intelligent file operations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run with default paths (continuous monitoring)
  python database_file_monitor_ROBUST.py

  # Run once with custom paths
  python database_file_monitor_ROBUST.py --source "C:\\Custom\\Path" --destination "D:\\Backup" --once

  # Run with debug logging to see robust operations in detail
  python database_file_monitor_ROBUST.py --log-level DEBUG

Key Improvements in this Robust Version:
  ✅ Replaces problematic sleep delays with intelligent file stability checking
  ✅ Implements retry mechanisms with exponential backoff for failed operations
  ✅ Adds file integrity checks (size stability, write completion verification)
  ✅ Includes comprehensive error handling and logging for file operations
  ✅ Ensures operations are atomic where possible to prevent corruption
  ✅ Uses file locking mechanisms to prevent concurrent access
        """
    )

    parser.add_argument(
        '--source', '-s',
        default=getattr(monitor_config, 'DEFAULT_SOURCE_PATH',
                       r"C:\Users\<USER>\OneDrive - Milson Elastomeric Solutions (Pty) Ltd\Documents\QC Database\BP TEST DATABASE"),
        help='Source folder to monitor (default: %(default)s)'
    )

    parser.add_argument(
        '--destination', '-d',
        default=getattr(monitor_config, 'DEFAULT_DESTINATION_PATH', r"B:\BP TEST DATABASE"),
        help='Destination folder for copies (default: %(default)s)'
    )

    parser.add_argument(
        '--once',
        action='store_true',
        help='Run once to copy existing files, then exit'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default=getattr(monitor_config, 'LOG_LEVEL', 'INFO'),
        help='Logging level (default: %(default)s)'
    )

    args = parser.parse_args()

    try:
        # Create robust monitor instance
        monitor = RobustDatabaseFileMonitor(
            source_path=args.source,
            destination_path=args.destination,
            log_level=args.log_level
        )

        # Run based on mode
        if args.once:
            monitor.run_once()
        else:
            monitor.run_continuous()

    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
