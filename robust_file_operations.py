#!/usr/bin/env python3
"""
Robust File Operations Module

Provides reliable file operations with proper verification, retry mechanisms,
exponential backoff, file locking, and integrity checks to prevent data corruption.

This module replaces simple file operations with robust alternatives that:
- Verify file write completion before proceeding
- Implement retry mechanisms with exponential backoff
- Check file integrity and stability
- Use file locking to prevent concurrent access
- Provide atomic operations where possible
- Include comprehensive error handling and logging

Author: Generated for Database File Monitor
Version: 1.0
"""

import os
import sys
import time
import shutil
import hashlib
import logging
import threading
from pathlib import Path
from typing import Optional, Callable, Any, Dict, Tuple
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum
# Platform-specific imports
try:
    import fcntl  # Unix systems
except ImportError:
    fcntl = None

try:
    import msvcrt  # Windows systems
except ImportError:
    msvcrt = None


class FileOperationError(Exception):
    """Custom exception for file operation failures"""
    pass


class FileIntegrityError(Exception):
    """Custom exception for file integrity check failures"""
    pass


class RetryExhaustedError(Exception):
    """Custom exception when retry attempts are exhausted"""
    pass


class OperationType(Enum):
    """Types of file operations"""
    COPY = "copy"
    MOVE = "move"
    DELETE = "delete"
    READ = "read"
    WRITE = "write"


@dataclass
class RetryConfig:
    """Configuration for retry mechanisms"""
    max_attempts: int = 5
    initial_delay: float = 0.1
    max_delay: float = 30.0
    backoff_factor: float = 2.0
    jitter: bool = True


@dataclass
class IntegrityConfig:
    """Configuration for file integrity checks"""
    check_size_stability: bool = True
    size_stability_checks: int = 3
    size_stability_interval: float = 0.1
    verify_checksum: bool = False
    checksum_algorithm: str = 'md5'
    max_wait_time: float = 60.0


@dataclass
class FileOperationConfig:
    """Complete configuration for robust file operations"""
    retry_config: RetryConfig
    integrity_config: IntegrityConfig
    use_file_locking: bool = True
    atomic_operations: bool = True
    preserve_metadata: bool = True
    verify_after_operation: bool = True


class FileStabilityChecker:
    """Utility class for checking file stability and write completion"""
    
    def __init__(self, config: IntegrityConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
    
    def wait_for_file_stability(self, file_path: Path, timeout: float = None) -> bool:
        """
        Wait for a file to become stable (no longer being written to)
        
        Args:
            file_path: Path to the file to check
            timeout: Maximum time to wait (uses config default if None)
            
        Returns:
            bool: True if file is stable, False if timeout reached
        """
        if timeout is None:
            timeout = self.config.max_wait_time
            
        start_time = time.time()
        
        if not file_path.exists():
            self.logger.warning(f"File does not exist for stability check: {file_path}")
            return False
        
        if self.config.check_size_stability:
            return self._check_size_stability(file_path, timeout, start_time)
        
        # If size stability check is disabled, just check if file exists and is accessible
        return self._check_file_accessibility(file_path)
    
    def _check_size_stability(self, file_path: Path, timeout: float, start_time: float) -> bool:
        """Check if file size remains stable over multiple checks"""
        previous_size = None
        stable_checks = 0
        
        while time.time() - start_time < timeout:
            try:
                current_size = file_path.stat().st_size
                
                if previous_size is not None and current_size == previous_size:
                    stable_checks += 1
                    if stable_checks >= self.config.size_stability_checks:
                        self.logger.debug(f"File size stable after {stable_checks} checks: {file_path}")
                        return True
                else:
                    stable_checks = 0
                
                previous_size = current_size
                time.sleep(self.config.size_stability_interval)
                
            except (OSError, IOError) as e:
                self.logger.debug(f"Error checking file size stability: {e}")
                time.sleep(self.config.size_stability_interval)
        
        self.logger.warning(f"File stability timeout reached: {file_path}")
        return False
    
    def _check_file_accessibility(self, file_path: Path) -> bool:
        """Check if file is accessible for reading"""
        try:
            with open(file_path, 'rb') as f:
                f.read(1)  # Try to read one byte
            return True
        except (OSError, IOError) as e:
            self.logger.debug(f"File not accessible: {e}")
            return False
    
    def verify_file_integrity(self, file_path: Path, expected_size: Optional[int] = None, 
                            expected_checksum: Optional[str] = None) -> bool:
        """
        Verify file integrity using size and/or checksum
        
        Args:
            file_path: Path to file to verify
            expected_size: Expected file size in bytes
            expected_checksum: Expected checksum value
            
        Returns:
            bool: True if integrity checks pass
        """
        try:
            if not file_path.exists():
                self.logger.error(f"File does not exist for integrity check: {file_path}")
                return False
            
            # Check file size if provided
            if expected_size is not None:
                actual_size = file_path.stat().st_size
                if actual_size != expected_size:
                    self.logger.error(f"Size mismatch: expected {expected_size}, got {actual_size}")
                    return False
            
            # Check checksum if enabled and provided
            if self.config.verify_checksum and expected_checksum is not None:
                actual_checksum = self._calculate_checksum(file_path)
                if actual_checksum != expected_checksum:
                    self.logger.error(f"Checksum mismatch: expected {expected_checksum}, got {actual_checksum}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error during integrity verification: {e}")
            return False
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate file checksum using configured algorithm"""
        hash_obj = hashlib.new(self.config.checksum_algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()


class FileLockManager:
    """Cross-platform file locking manager"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self._locks: Dict[str, threading.Lock] = {}
        self._lock_registry_lock = threading.Lock()

    @contextmanager
    def lock_file(self, file_path: Path, exclusive: bool = True, timeout: float = 30.0):
        """
        Context manager for file locking

        Args:
            file_path: Path to file to lock
            exclusive: Whether to use exclusive lock (True) or shared lock (False)
            timeout: Maximum time to wait for lock acquisition

        Yields:
            file handle if successful

        Raises:
            FileOperationError: If lock cannot be acquired
        """
        file_str = str(file_path.absolute())

        # Get or create a thread lock for this file
        with self._lock_registry_lock:
            if file_str not in self._locks:
                self._locks[file_str] = threading.Lock()
            thread_lock = self._locks[file_str]

        # Acquire thread lock first
        if not thread_lock.acquire(timeout=timeout):
            raise FileOperationError(f"Could not acquire thread lock for {file_path}")

        try:
            # Open file and acquire OS-level lock
            mode = 'r+b' if file_path.exists() else 'w+b'
            file_handle = open(file_path, mode)

            try:
                if sys.platform == 'win32':
                    self._acquire_windows_lock(file_handle, exclusive, timeout)
                else:
                    self._acquire_unix_lock(file_handle, exclusive, timeout)

                self.logger.debug(f"Acquired {'exclusive' if exclusive else 'shared'} lock: {file_path}")
                yield file_handle

            finally:
                # Release OS-level lock
                try:
                    if sys.platform == 'win32':
                        msvcrt.locking(file_handle.fileno(), msvcrt.LK_UNLCK, 1)
                    else:
                        fcntl.flock(file_handle.fileno(), fcntl.LOCK_UN)
                    self.logger.debug(f"Released lock: {file_path}")
                except:
                    pass  # Lock might already be released

                file_handle.close()

        finally:
            # Release thread lock
            thread_lock.release()

    def _acquire_windows_lock(self, file_handle, exclusive: bool, timeout: float):
        """Acquire file lock on Windows"""
        if not msvcrt:
            raise FileOperationError("Windows file locking not available")

        lock_type = msvcrt.LK_NBLCK if exclusive else msvcrt.LK_NBRLCK
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                msvcrt.locking(file_handle.fileno(), lock_type, 1)
                return
            except OSError:
                time.sleep(0.1)

        raise FileOperationError(f"Could not acquire Windows file lock within {timeout} seconds")

    def _acquire_unix_lock(self, file_handle, exclusive: bool, timeout: float):
        """Acquire file lock on Unix-like systems"""
        if not fcntl:
            raise FileOperationError("Unix file locking not available")

        lock_type = fcntl.LOCK_EX if exclusive else fcntl.LOCK_SH
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                fcntl.flock(file_handle.fileno(), lock_type | fcntl.LOCK_NB)
                return
            except (OSError, IOError):
                time.sleep(0.1)

        raise FileOperationError(f"Could not acquire Unix file lock within {timeout} seconds")


class RetryManager:
    """Manages retry logic with exponential backoff"""

    def __init__(self, config: RetryConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger

    def retry_operation(self, operation: Callable, operation_name: str,
                       *args, **kwargs) -> Any:
        """
        Execute operation with retry logic and exponential backoff

        Args:
            operation: Function to execute
            operation_name: Name for logging purposes
            *args, **kwargs: Arguments to pass to operation

        Returns:
            Result of successful operation

        Raises:
            RetryExhaustedError: If all retry attempts fail
        """
        last_exception = None
        delay = self.config.initial_delay

        for attempt in range(1, self.config.max_attempts + 1):
            try:
                self.logger.debug(f"Attempting {operation_name} (attempt {attempt}/{self.config.max_attempts})")
                result = operation(*args, **kwargs)

                if attempt > 1:
                    self.logger.info(f"✅ {operation_name} succeeded on attempt {attempt}")

                return result

            except Exception as e:
                last_exception = e
                self.logger.warning(f"❌ {operation_name} failed on attempt {attempt}: {e}")

                if attempt < self.config.max_attempts:
                    # Calculate next delay with exponential backoff
                    if self.config.jitter:
                        import random
                        jitter_factor = 0.1 * random.random()  # 0-10% jitter
                        actual_delay = delay * (1 + jitter_factor)
                    else:
                        actual_delay = delay

                    self.logger.debug(f"Waiting {actual_delay:.2f}s before retry...")
                    time.sleep(actual_delay)

                    # Increase delay for next attempt
                    delay = min(delay * self.config.backoff_factor, self.config.max_delay)

        # All attempts failed
        raise RetryExhaustedError(
            f"Operation '{operation_name}' failed after {self.config.max_attempts} attempts. "
            f"Last error: {last_exception}"
        )


class RobustFileOperations:
    """
    Main class providing robust file operations with comprehensive error handling,
    retry mechanisms, file locking, and integrity verification.
    """

    def __init__(self, config: FileOperationConfig = None, logger: logging.Logger = None):
        """
        Initialize robust file operations

        Args:
            config: Configuration for file operations
            logger: Logger instance for operation logging
        """
        self.config = config or FileOperationConfig(
            retry_config=RetryConfig(),
            integrity_config=IntegrityConfig()
        )

        self.logger = logger or logging.getLogger(__name__)

        # Initialize components
        self.stability_checker = FileStabilityChecker(self.config.integrity_config, self.logger)
        self.lock_manager = FileLockManager(self.logger)
        self.retry_manager = RetryManager(self.config.retry_config, self.logger)

    def robust_copy(self, source_path: Path, destination_path: Path,
                   verify_integrity: bool = None) -> bool:
        """
        Perform a robust file copy operation with all safety checks

        Args:
            source_path: Source file path
            destination_path: Destination file path
            verify_integrity: Whether to verify integrity (uses config default if None)

        Returns:
            bool: True if copy successful, False otherwise

        Raises:
            FileOperationError: If operation fails after all retries
        """
        if verify_integrity is None:
            verify_integrity = self.config.verify_after_operation

        def _copy_operation():
            return self._perform_copy(source_path, destination_path, verify_integrity)

        try:
            return self.retry_manager.retry_operation(
                _copy_operation,
                f"copy {source_path.name} -> {destination_path.name}"
            )
        except RetryExhaustedError as e:
            self.logger.error(f"Robust copy failed: {e}")
            raise FileOperationError(str(e))

    def _perform_copy(self, source_path: Path, destination_path: Path,
                     verify_integrity: bool) -> bool:
        """Internal method to perform the actual copy operation"""

        # Step 1: Wait for source file stability
        self.logger.debug(f"Checking source file stability: {source_path}")
        if not self.stability_checker.wait_for_file_stability(source_path):
            raise FileOperationError(f"Source file not stable: {source_path}")

        # Step 2: Get source file info for verification
        source_stat = source_path.stat()
        source_size = source_stat.st_size
        source_checksum = None

        if verify_integrity and self.config.integrity_config.verify_checksum:
            source_checksum = self.stability_checker._calculate_checksum(source_path)

        # Step 3: Prepare destination (create temp file for atomic operation)
        if self.config.atomic_operations:
            temp_destination = destination_path.with_suffix(destination_path.suffix + '.tmp')
        else:
            temp_destination = destination_path

        # Step 4: Perform copy with file locking
        try:
            if self.config.use_file_locking:
                with self.lock_manager.lock_file(source_path, exclusive=False):
                    self._copy_file_content(source_path, temp_destination)
            else:
                self._copy_file_content(source_path, temp_destination)

            # Step 5: Verify copied file
            if verify_integrity:
                if not self.stability_checker.verify_file_integrity(
                    temp_destination, source_size, source_checksum
                ):
                    if temp_destination.exists():
                        temp_destination.unlink()
                    raise FileIntegrityError("Copy verification failed")

            # Step 6: Atomic move if using temporary file
            if self.config.atomic_operations and temp_destination != destination_path:
                temp_destination.replace(destination_path)

            self.logger.info(f"✅ Robust copy completed: {source_path.name} -> {destination_path.name}")
            return True

        except Exception as e:
            # Clean up temporary file on failure
            if self.config.atomic_operations and temp_destination.exists() and temp_destination != destination_path:
                try:
                    temp_destination.unlink()
                except:
                    pass
            raise e

    def _copy_file_content(self, source_path: Path, destination_path: Path):
        """Copy file content with metadata preservation"""
        if self.config.preserve_metadata:
            shutil.copy2(source_path, destination_path)
        else:
            shutil.copy(source_path, destination_path)

    def wait_for_file_ready(self, file_path: Path, timeout: float = None) -> bool:
        """
        Wait for a file to be ready for operations (stable and accessible)

        Args:
            file_path: Path to file to check
            timeout: Maximum time to wait

        Returns:
            bool: True if file is ready, False if timeout
        """
        return self.stability_checker.wait_for_file_stability(file_path, timeout)

    def verify_file_integrity(self, file_path: Path, expected_size: Optional[int] = None,
                            expected_checksum: Optional[str] = None) -> bool:
        """
        Verify file integrity

        Args:
            file_path: Path to file to verify
            expected_size: Expected file size
            expected_checksum: Expected checksum

        Returns:
            bool: True if integrity checks pass
        """
        return self.stability_checker.verify_file_integrity(file_path, expected_size, expected_checksum)


# Convenience functions for easy integration
def create_default_config() -> FileOperationConfig:
    """Create a default configuration for robust file operations"""
    return FileOperationConfig(
        retry_config=RetryConfig(
            max_attempts=5,
            initial_delay=0.1,
            max_delay=30.0,
            backoff_factor=2.0,
            jitter=True
        ),
        integrity_config=IntegrityConfig(
            check_size_stability=True,
            size_stability_checks=3,
            size_stability_interval=0.1,
            verify_checksum=False,  # Disabled by default for performance
            checksum_algorithm='md5',
            max_wait_time=60.0
        ),
        use_file_locking=True,
        atomic_operations=True,
        preserve_metadata=True,
        verify_after_operation=True
    )


def create_performance_config() -> FileOperationConfig:
    """Create a performance-optimized configuration"""
    return FileOperationConfig(
        retry_config=RetryConfig(
            max_attempts=3,
            initial_delay=0.05,
            max_delay=10.0,
            backoff_factor=1.5,
            jitter=True
        ),
        integrity_config=IntegrityConfig(
            check_size_stability=True,
            size_stability_checks=2,
            size_stability_interval=0.05,
            verify_checksum=False,
            max_wait_time=30.0
        ),
        use_file_locking=True,
        atomic_operations=True,
        preserve_metadata=True,
        verify_after_operation=True
    )


def create_high_integrity_config() -> FileOperationConfig:
    """Create a high-integrity configuration with comprehensive checks"""
    return FileOperationConfig(
        retry_config=RetryConfig(
            max_attempts=7,
            initial_delay=0.2,
            max_delay=60.0,
            backoff_factor=2.0,
            jitter=True
        ),
        integrity_config=IntegrityConfig(
            check_size_stability=True,
            size_stability_checks=5,
            size_stability_interval=0.2,
            verify_checksum=True,
            checksum_algorithm='sha256',
            max_wait_time=120.0
        ),
        use_file_locking=True,
        atomic_operations=True,
        preserve_metadata=True,
        verify_after_operation=True
    )


# Example usage and testing functions
if __name__ == "__main__":
    # Example usage
    import tempfile

    # Setup logging
    logging.basicConfig(level=logging.DEBUG)
    logger = logging.getLogger(__name__)

    # Create robust file operations instance
    config = create_default_config()
    robust_ops = RobustFileOperations(config, logger)

    # Example: Create test files and perform robust copy
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create source file
        source_file = temp_path / "test_source.txt"
        source_file.write_text("This is a test file for robust operations.")

        # Destination file
        dest_file = temp_path / "test_destination.txt"

        try:
            # Perform robust copy
            success = robust_ops.robust_copy(source_file, dest_file)
            print(f"Copy operation successful: {success}")

            # Verify the copy
            if dest_file.exists():
                print(f"Destination file exists: {dest_file.read_text()}")

        except FileOperationError as e:
            print(f"File operation failed: {e}")
        except Exception as e:
            print(f"Unexpected error: {e}")
