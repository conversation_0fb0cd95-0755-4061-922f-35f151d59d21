# Database File Monitor Dependencies
watchdog>=3.0.0

# Additional dependencies for robust file operations
# (All dependencies are part of Python standard library)
# - pathlib (Python 3.4+)
# - threading (built-in)
# - hashlib (built-in)
# - fcntl (Unix systems, built-in)
# - msvcrt (Windows systems, built-in)
# - contextlib (built-in)
# - dataclasses (Python 3.7+)
# - enum (built-in)
# - time (built-in)
# - logging (built-in)