#!/usr/bin/env python3
"""
Test script to demonstrate the robust file operations improvements

This script creates test scenarios to show how the robust file operations
handle various situations that would cause corruption with the old approach.
"""

import os
import sys
import time
import logging
import tempfile
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# Import our robust operations
from robust_file_operations import (
    RobustFileOperations,
    create_default_config,
    create_performance_config,
    create_high_integrity_config,
    FileOperationError
)


def setup_logging():
    """Setup logging for the test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


def create_test_database_file(file_path: Path, size_mb: float = 1.0):
    """Create a test database file with specified size"""
    # Create a file that simulates a database being written
    content = b"Access Database Test File\n" + b"X" * int(size_mb * 1024 * 1024 - 26)
    
    # Simulate slow writing (like a real database save)
    with open(file_path, 'wb') as f:
        chunk_size = 8192
        for i in range(0, len(content), chunk_size):
            f.write(content[i:i + chunk_size])
            f.flush()
            time.sleep(0.01)  # Simulate slow write


def test_file_stability_checking(logger, temp_dir):
    """Test that file stability checking works correctly"""
    logger.info("🧪 Testing file stability checking...")
    
    config = create_default_config()
    robust_ops = RobustFileOperations(config, logger)
    
    source_file = temp_dir / "test_stability.mdb"
    dest_file = temp_dir / "dest_stability.mdb"
    
    # Start writing file in background
    def slow_write():
        create_test_database_file(source_file, 0.5)
    
    with ThreadPoolExecutor() as executor:
        # Start writing file
        write_future = executor.submit(slow_write)
        
        # Try to copy while file is being written
        time.sleep(0.1)  # Let writing start
        
        try:
            success = robust_ops.robust_copy(source_file, dest_file)
            if success:
                logger.info("✅ File stability checking worked - copy succeeded after file was stable")
            else:
                logger.warning("⚠️ Copy failed - this might be expected if file wasn't stable")
        except FileOperationError as e:
            logger.info(f"✅ File stability checking worked - operation failed safely: {e}")
        
        # Wait for writing to complete
        write_future.result()
    
    # Now try copying the completed file
    try:
        success = robust_ops.robust_copy(source_file, dest_file)
        if success:
            logger.info("✅ Copy succeeded after file was completely written")
        else:
            logger.error("❌ Copy failed even after file was complete")
    except FileOperationError as e:
        logger.error(f"❌ Unexpected error: {e}")


def test_retry_mechanism(logger, temp_dir):
    """Test retry mechanism with simulated failures"""
    logger.info("🧪 Testing retry mechanism...")
    
    config = create_default_config()
    config.retry_config.max_attempts = 3
    config.retry_config.initial_delay = 0.1
    robust_ops = RobustFileOperations(config, logger)
    
    source_file = temp_dir / "test_retry.mdb"
    dest_file = temp_dir / "dest_retry.mdb"
    
    # Create source file
    create_test_database_file(source_file, 0.1)
    
    # Create a destination that will cause permission issues initially
    dest_file.touch()
    
    try:
        success = robust_ops.robust_copy(source_file, dest_file)
        if success:
            logger.info("✅ Retry mechanism worked - operation eventually succeeded")
        else:
            logger.warning("⚠️ Operation failed after retries")
    except FileOperationError as e:
        logger.info(f"✅ Retry mechanism exhausted gracefully: {e}")


def test_performance_comparison(logger, temp_dir):
    """Compare performance of different configurations"""
    logger.info("🧪 Testing performance comparison...")
    
    # Create test file
    source_file = temp_dir / "test_performance.mdb"
    create_test_database_file(source_file, 2.0)  # 2MB file
    
    configs = {
        "Performance": create_performance_config(),
        "Default": create_default_config(),
        "High Integrity": create_high_integrity_config()
    }
    
    for config_name, config in configs.items():
        dest_file = temp_dir / f"dest_{config_name.lower().replace(' ', '_')}.mdb"
        
        robust_ops = RobustFileOperations(config, logger)
        
        start_time = time.time()
        try:
            success = robust_ops.robust_copy(source_file, dest_file)
            elapsed = time.time() - start_time
            
            if success:
                logger.info(f"✅ {config_name} config: {elapsed:.2f}s")
            else:
                logger.warning(f"⚠️ {config_name} config failed")
        except Exception as e:
            logger.error(f"❌ {config_name} config error: {e}")


def main():
    """Run all tests"""
    logger = setup_logging()
    logger.info("🚀 Starting robust file operations tests...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        logger.info(f"Using temporary directory: {temp_path}")
        
        try:
            # Run tests
            test_file_stability_checking(logger, temp_path)
            print()
            
            test_retry_mechanism(logger, temp_path)
            print()
            
            test_performance_comparison(logger, temp_path)
            print()
            
            logger.info("✅ All tests completed!")
            
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
