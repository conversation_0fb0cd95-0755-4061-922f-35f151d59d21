"""
Configuration file for Database File Monitor
Modify these settings as needed for your environment
"""

# Default paths
DEFAULT_SOURCE_PATH = r"C:\Users\<USER>\OneDrive - Milson Elastomeric Solutions (Pty) Ltd\Documents\QC Database\BP TEST DATABASE"
DEFAULT_DESTINATION_PATH = r"B:\BP TEST DATABASE"

# File extensions to monitor
DATABASE_EXTENSIONS = {'.mdb', '.accdb'}

# Monitoring settings
MONITOR_INTERVAL_SECONDS = 10  # How often to check the monitoring loop

# Logging settings
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_DIRECTORY = "logs"
LOG_FILE_PREFIX = "database_monitor"

# Backup settings
CREATE_BACKUPS = True  # Create backups when overwriting existing files
BACKUP_SUFFIX = "_backup"

# Robust File Operations Configuration
# These settings replace the old COPY_DELAY_SECONDS and simple retry logic

# Retry mechanism settings
ROBUST_RETRY_MAX_ATTEMPTS = 5  # Maximum number of retry attempts
ROBUST_RETRY_INITIAL_DELAY = 0.1  # Initial delay in seconds
ROBUST_RETRY_MAX_DELAY = 30.0  # Maximum delay between retries
ROBUST_RETRY_BACKOFF_FACTOR = 2.0  # Exponential backoff multiplier
ROBUST_RETRY_USE_JITTER = True  # Add random jitter to prevent thundering herd

# File integrity and stability settings
ROBUST_CHECK_SIZE_STABILITY = True  # Check if file size is stable before operations
ROBUST_SIZE_STABILITY_CHECKS = 3  # Number of consecutive stable size checks required
ROBUST_SIZE_STABILITY_INTERVAL = 0.1  # Interval between stability checks (seconds)
ROBUST_VERIFY_CHECKSUM = False  # Enable checksum verification (slower but more secure)
ROBUST_CHECKSUM_ALGORITHM = 'md5'  # Checksum algorithm: md5, sha1, sha256
ROBUST_MAX_WAIT_TIME = 60.0  # Maximum time to wait for file stability

# File operation settings
ROBUST_USE_FILE_LOCKING = True  # Use file locking to prevent concurrent access
ROBUST_ATOMIC_OPERATIONS = True  # Use atomic operations (temp files + rename)
ROBUST_PRESERVE_METADATA = True  # Preserve file timestamps and permissions
ROBUST_VERIFY_AFTER_OPERATION = True  # Verify file integrity after operations

# Performance profiles - uncomment one to use
# ROBUST_PROFILE = "default"  # Balanced performance and reliability
# ROBUST_PROFILE = "performance"  # Optimized for speed
ROBUST_PROFILE = "high_integrity"  # Maximum reliability and verification

# Legacy settings (deprecated - use robust settings above)
# COPY_DELAY_SECONDS = 1  # DEPRECATED: Replaced by robust file stability checking
# MAX_RETRY_ATTEMPTS = 3  # DEPRECATED: Use ROBUST_RETRY_MAX_ATTEMPTS
# RETRY_DELAY_SECONDS = 5  # DEPRECATED: Use robust retry configuration