"""
Configuration file for Database File Monitor
Modify these settings as needed for your environment
"""

# Default paths
DEFAULT_SOURCE_PATH = r"C:\Users\<USER>\OneDrive - Milson Elastomeric Solutions (Pty) Ltd\Documents\QC Database\BP TEST DATABASE"
DEFAULT_DESTINATION_PATH = r"B:\BP TEST DATABASE"

# File extensions to monitor
DATABASE_EXTENSIONS = {'.mdb', '.accdb'}

# Monitoring settings
COPY_DELAY_SECONDS = 1  # Wait time after file creation before copying
MON<PERSON>OR_INTERVAL_SECONDS = 10  # How often to check the monitoring loop

# Logging settings
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_DIRECTORY = "logs"
LOG_FILE_PREFIX = "database_monitor"

# Backup settings
CREATE_BACKUPS = True  # Create backups when overwriting existing files
BACKUP_SUFFIX = "_backup"

# Error handling
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 5