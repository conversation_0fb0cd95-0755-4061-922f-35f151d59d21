@echo off
echo Starting Database File Monitor...
echo.

REM Change to script directory
cd /d "%~dp0"

REM Install dependencies if needed
python -m pip install -r requirements_monitor.txt

REM Start the monitor
start "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe" database_file_monitor.py
start "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe" Access_Table_to_CSV_Converter.py

pause